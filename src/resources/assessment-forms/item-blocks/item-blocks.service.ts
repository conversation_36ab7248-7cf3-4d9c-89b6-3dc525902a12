import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateItemBlockDto } from '../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../dto/updates/update-item-block.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { ItemBlock } from '../entities/item-block.entity';
import { In, Repository, type EntityManager } from 'typeorm';
import { QuestionsService } from '../questions/questions.service';
import { ItemBlockType } from '../enums/item-block-type.enum';
import type { UpdateItemBlockSequencesDto } from '../dto/updates/ีupdate-block-sequence.dto';
import { OptionsService } from '../options/options.service';
import { Assessment } from '../entities/assessment.entity';
import { HeaderBody } from '../entities/header-body.entity';
import { ImageBody } from '../entities/image-body.entity';
import { Submission } from '../entities/submission.entity';
import { Question } from '../entities/question.entity';
import { Option } from '../entities/option.entity';
import { AssessmentConfig } from 'src/configs/assessment.config';

@Injectable()
export class ItemBlocksService {
  constructor(
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    private readonly questionService: QuestionsService,
    private readonly optionsService: OptionsService,
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(Option)
    private readonly optionRepository: Repository<Option>,
    @InjectRepository(HeaderBody)
    private readonly headerBodyRepository: Repository<HeaderBody>,
    @InjectRepository(ImageBody)
    private readonly imageBodyRepository: Repository<ImageBody>,
  ) {}

  async getMaxSequence(assessmentId: number): Promise<number> {
    const lastItemBlock = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.sequence', 'DESC')
      .getOne();

    return lastItemBlock ? lastItemBlock.sequence + 1 : 1;
  }

  async getMaxSectionNumber(assessmentId: number): Promise<number> {
    const lastSection = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.section', 'DESC')
      .getOne();

    return lastSection ? lastSection.section + 1 : 1;
  }

  async handleType(type: string, savedItemBlock: ItemBlock) {
    let result = savedItemBlock;

    const createQuestionAndOption = async (
      itemBlockId: number,
      includeHeader: boolean = false,
      withOption: boolean = true,
    ) => {
      const questions = [];

      if (includeHeader) {
        const headerQuestion = await this.questionService.create({
          itemBlockId,
          isHeader: false,
        });
        const fullHeaderQuestion = await this.questionService.findOne(
          headerQuestion.id,
        );
        questions.push(fullHeaderQuestion);
      }

      const question = await this.questionService.create({
        itemBlockId,
        isHeader: true,
      });
      const fullQuestion = await this.questionService.findOne(question.id);
      questions.push(fullQuestion);

      result.questions = questions;

      if (withOption) {
        const option = await this.optionsService.create({
          itemBlockId,
          value: 1,
          sequence: 1,
          nextSection: 1,
        });
        const fullOption = await this.optionsService.findOne(option.id);
        result.options = [fullOption];
      }
    };

    // Type not to create Question and Option
    const shouldCreateQuestionAndOption = ![
      ItemBlockType.HEADER,
      ItemBlockType.IMAGE,
    ].includes(type as ItemBlockType);

    if (shouldCreateQuestionAndOption) {
      // if type = Grid set includeHeader = true
      const includeHeader = type === ItemBlockType.GRID;
      // want to create Option
      const withOption = [
        ItemBlockType.RADIO,
        ItemBlockType.CHECKBOX,
        ItemBlockType.GRID,
      ].includes(type as ItemBlockType);
      await createQuestionAndOption(
        savedItemBlock.id,
        includeHeader,
        withOption,
      );
    }
    // Type is Header
    else if (
      shouldCreateQuestionAndOption === false &&
      type === ItemBlockType.HEADER
    ) {
      const headerBody = this.headerBodyRepository.create({
        title: AssessmentConfig.DEFAULT_HEADER_TITLE,
        description: AssessmentConfig.DEFAULT_HEADER_DESCRIPTION,
        itemBlockId: savedItemBlock.id,
      });
      const savedHeaderBody = await this.headerBodyRepository.save(headerBody);
      result.headerBody = savedHeaderBody;
    }
    // Type is Image
    else if (
      shouldCreateQuestionAndOption === false &&
      type === ItemBlockType.IMAGE
    ) {
      const imageBody = this.imageBodyRepository.create({
        itemBlockId: savedItemBlock.id,
      });
      const savedImageBody = await this.imageBodyRepository.save(imageBody);
      result.imageBody = savedImageBody;
    }

    return result;
  }

  // async createHeaderTemplate(assessmentId: number) {
  //   const nextSequence =
  //     await this.itemBlockHelper.getMaxSequence(assessmentId);
  //   const nextSection =
  //     await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

  //   const { block: savedHeaderBlock, body: savedHeaderBody } =
  //     await this.createHeaderBlockWithBody(
  //       assessmentId,
  //       nextSequence,
  //       nextSection,
  //     );
  //   const nextSequence2 =
  //     await this.itemBlockHelper.getMaxSequence(assessmentId);
  //   const nextSection2 =
  //     await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

  //   const { block: savedHeaderBlock2, body: savedHeaderBody2 } =
  //     await this.createHeaderBlockWithBody(
  //       assessmentId,
  //       nextSequence2,
  //       nextSection2,
  //     );

  //   return {
  //     id: savedHeaderBlock.id,
  //     sequence: savedHeaderBlock.sequence,
  //     section: savedHeaderBlock.section,
  //     type: savedHeaderBlock.type,
  //     headerBody: {
  //       id: savedHeaderBody.id,
  //       title: savedHeaderBody.title,
  //       description: savedHeaderBody.description,
  //     },
  //   };
  // }

  // async createQuestionTemplate(assessmentId: number) {
  //   const nextSequence =
  //     await this.itemBlockHelper.getMaxSequence(assessmentId);
  //   const nextSection =
  //     await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

  //   const { block: savedQuestionBlock, body: savedQuestionBody } =
  //     await this.createQuestionBlockWithBody(
  //       assessmentId,
  //       nextSequence,
  //       nextSection,
  //     );

  //   const { body: savedOptionBlock } = await this.createOption(
  //     savedQuestionBlock.id,
  //   );

  //   return {
  //     id: savedQuestionBlock.id,
  //     sequence: savedQuestionBlock.sequence,
  //     section: savedQuestionBlock.section,
  //     type: savedQuestionBlock.type,
  //     isRequired: savedQuestionBlock.isRequired,
  //     question: {
  //       id: savedQuestionBody.id,
  //       questionText: savedQuestionBody.questionText,
  //       imagePath: savedQuestionBody.imagePath,
  //       isHeader: savedQuestionBody.isHeader,
  //       sequence: savedQuestionBody.sequence,
  //       sizeLimit: savedQuestionBody.sizeLimit,
  //       acceptFile: savedQuestionBody.acceptFile,
  //       uploadLimit: savedQuestionBody.uploadLimit,
  //     },
  //     options: [
  //       {
  //         id: savedOptionBlock.id,
  //         optionText: savedOptionBlock.optionText,
  //         imagePath: savedOptionBlock.imagePath,
  //         value: savedOptionBlock.value,
  //         sequence: savedOptionBlock.sequence,
  //         nextSection: savedOptionBlock.nextSection,
  //       },
  //     ],
  //   };
  // }

  async createEmptyBlock({
    assessmentId,
    sequence,
    section,
    type,
  }: {
    assessmentId: number;
    sequence?: number;
    section?: number;
    type?: ItemBlockType;
  }): Promise<ItemBlock> {
    //this method will create a block with no body
    const block = this.itemBlockRepository.create({
      sequence,
      section,
      type: type,
      isRequired: false,
      assessment: { id: assessmentId },
    });

    return await this.itemBlockRepository.save(block);
  }

  /**
   * Updates the sequence of multiple item blocks simultaneously using a safe and optimized bulk update approach
   * @param updateSequencesDto DTO containing an array of item blocks with their new sequence numbers
   * @returns Object with success status and updated item blocks
   */
  async updateSequences(updateSequencesDto: UpdateItemBlockSequencesDto) {
    const { itemBlocks } = updateSequencesDto;

    if (!itemBlocks || itemBlocks.length === 0) {
      throw new BadRequestException(
        'No item blocks provided for sequence update',
      );
    }

    const itemBlockIds = itemBlocks.map((item) => item.id);

    const queryRunner =
      this.itemBlockRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingBlocks = await this.itemBlockRepository.find({
        where: { id: In(itemBlockIds) },
        select: ['id', 'sequence', 'assessmentId'],
      });

      if (existingBlocks.length !== itemBlockIds.length) {
        const foundIds = existingBlocks.map((block) => block.id);
        const missingIds = itemBlockIds.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Item blocks with IDs ${missingIds.join(', ')} not found`,
        );
      }

      const caseStatements = itemBlocks
        .map(() => `WHEN id = ? THEN ?`)
        .join(' ');

      const wherePlaceholders = itemBlocks.map(() => `?`).join(',');

      // สร้าง params: [id1, seq1, id2, seq2, ..., idN, seqN, id1, id2, ..., idN]
      const params: any[] = [];
      itemBlocks.forEach((item) => {
        params.push(item.id, item.sequence);
      });
      itemBlocks.forEach((item) => {
        params.push(item.id);
      });

      const rawQuery = `
    UPDATE item_blocks
    SET sequence = CASE ${caseStatements} ELSE sequence END
    WHERE id IN (${wherePlaceholders})
  `;

      await queryRunner.manager.query(rawQuery, params);

      await queryRunner.commitTransaction();

      const updatedBlocks = await this.itemBlockRepository.find({
        where: { id: In(itemBlockIds) },
      });

      return {
        success: true,
        message: 'Item block sequences updated successfully',
        data: updatedBlocks.map((block) => ({
          id: block.id,
          sequence: block.sequence,
        })),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      throw new InternalServerErrorException(
        `Failed to update item block sequences: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async createBlock(dto: CreateItemBlockDto) {
    const { assessmentId, type, sequence, ...rest } = dto;

    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
    });
    if (!assessment) {
      throw new NotFoundException(
        `Assessment with ID ${assessmentId} not found`,
      );
    }

    // ✅ สร้าง ItemBlock โดยไม่ bind header/image body
    const itemBlock = this.itemBlockRepository.create({
      ...rest,
      isRequired: rest.isRequired ?? false,
      type,
      sequence: sequence ?? (await this.getMaxSequence(assessmentId)),
      assessment,
    });

    const saved = await this.itemBlockRepository.save(itemBlock);

    // ✅ เรียก handleType แยก logic เช่นสร้าง header/image/option/question ตาม type
    await this.handleType(type, saved);

    // ✅ ดึงข้อมูลแยก relation แบบ manual เฉพาะที่ต้องการ (ไม่ preload headerBody/imageBody โดยไม่จำเป็น)
    const questions = await this.questionRepository.find({
      where: { itemBlock: { id: saved.id } },
    });

    const options = await this.optionRepository.find({
      where: { itemBlock: { id: saved.id } },
    });

    const headerBody = await this.headerBodyRepository.findOne({
      where: { itemBlockId: saved.id },
    });

    const imageBody = await this.imageBodyRepository.findOne({
      where: { itemBlockId: saved.id },
    });

    return {
      id: saved.id,
      sequence: saved.sequence,
      section: saved.section,
      type: saved.type,
      isRequired: saved.isRequired ?? false,
      assessmentId: saved.assessment.id,
      questions: questions.length > 0 ? questions : null,
      options: options.length > 0 ? options : null,
      headerBody: headerBody ?? null,
      imageBody: imageBody ?? null,
    };
  }

  // async createBlockHeader(dto: CreateItemBlockDto) {
  //   const assessment = await this.assessmentRepo.findOne({
  //     where: { id: dto.assessmentId },
  //   });
  //   const itemBlock = this.itemBlockRepository.create({
  //     type: ItemBlockType.HEADER,
  //     sequence: dto.sequence ?? 1,
  //     section: dto.section ?? 1,
  //     isRequired: false,
  //     assessment,
  //   });
  //   const savedBlock = await this.itemBlockRepository.save(itemBlock);
  //   const headerBody = await this.headerBodiesService.createHeader({
  //     itemBlockId: savedBlock.id,
  //   });

  //   return {
  //     id: savedBlock.id,
  //     type: savedBlock.type,
  //     sequence: savedBlock.sequence,
  //     section: savedBlock.section,
  //     isRequired: savedBlock.isRequired,
  //     assessmentId: savedBlock.assessment.id,
  //     headerBody,
  //   };
  // }

  findAll(assessmentId: number, page: number) {
    return this.itemBlockRepository.find({
      where: {
        assessment: { id: assessmentId },
        section: page,
      },
      relations: ['questions', 'options'],
    });
  }

  findOne(assessmentId: number) {
    return this.itemBlockRepository.findOne({
      where: {
        assessment: { id: assessmentId },
      },
      relations: ['questions', 'options'],
    });
  }

  async updateOne(
    id: number,
    updateItemBlockDto: UpdateItemBlockDto,
  ): Promise<ItemBlock> {
    const item = await this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    if (!item) {
      throw new NotFoundException(`ItemBlock with ID ${id} not found`);
    }

    const oldType = item.type;
    const newType = updateItemBlockDto.type;

    const updatedItem = Object.assign(item, updateItemBlockDto);
    const savedItem = await this.itemBlockRepository.save(updatedItem);

    // === Type Check Helpers ===
    const typeHasOptions = (type: string) =>
      [
        ItemBlockType.RADIO,
        ItemBlockType.CHECKBOX,
        ItemBlockType.GRID,
      ].includes(type as ItemBlockType);

    const typeHasQuestion = (type: string) =>
      ![ItemBlockType.IMAGE, ItemBlockType.HEADER].includes(
        type as ItemBlockType,
      );

    const isGrid = (type: string) => type === ItemBlockType.GRID;
    const isTextField = (type: string) => type === ItemBlockType.TEXTFIELD;

    // === 1. GRID → อื่น: ลบ non-header questions ===
    if (isGrid(oldType) && !isGrid(newType)) {
      for (const q of item.questions) {
        if (!q.isHeader) {
          console.log('Grid Remove');
          await this.questionService.remove(q.id);
        } else {
          await this.questionService.update(q.id, q);
        }
      }
    }

    if (!isGrid(oldType) && isGrid(newType)) {
      const question = await this.questionService.create({
        itemBlockId: id,
        isHeader: false,
      });
      savedItem.questions = [
        ...(savedItem.questions || []),
        await this.questionService.findOne(question.id),
      ];
    }

    // === 2. TEXTFIELD → type ที่มี option: สร้าง option ใหม่ ===
    if (isTextField(oldType) && typeHasOptions(newType)) {
      const newOption = await this.optionsService.create({
        itemBlockId: id,
        value: 1,
        sequence: 1,
        nextSection: 1,
      });
      savedItem.options = [await this.optionsService.findOne(newOption.id)];
    }

    // === 3. มี options → มี options: อัปเดต options เดิม ===
    else if (typeHasOptions(oldType) && typeHasOptions(newType)) {
      for (const option of item.options) {
        await this.optionsService.update(option.id, option);
      }
    }

    // === 4. type ใหม่ไม่มี option → ลบ option ===
    if (!typeHasOptions(newType)) {
      await this.optionsService.removeByItemBlockId(id);
    }

    // === 5. type ใหม่ไม่มี question → ลบ question ===
    if (!typeHasQuestion(newType)) {
      await this.questionService.removeByItemBlockId(id);
    }

    return this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });
  }

  async removeOne(id: number): Promise<void> {
    const item = await this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    if (!item) {
      throw new NotFoundException(`ItemBlock with ID ${id} not found`);
    }

    // ลบข้อมูลสัมพันธ์
    if (item.questions?.length) {
      await this.questionService.removeByItemBlockId(id);
    }

    if (item.options?.length) {
      await this.optionsService.removeByItemBlockId(id);
    }

    if (item.headerBody) {
      await this.headerBodyRepository.delete({ itemBlockId: id });
    }

    if (item.imageBody) {
      await this.imageBodyRepository.delete({ itemBlockId: id });
    }

    // ลบ itemBlock หลัก
    await this.itemBlockRepository.delete(id);
  }
  // item-blocks.service.ts

  async removeAllByAssessmentId(assessmentId: number): Promise<void> {
    const itemBlocks = await this.itemBlockRepository.find({
      where: { assessment: { id: assessmentId } },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    for (const block of itemBlocks) {
      await this.itemBlockRepository.remove(block);
    }
  }

  async sequenceQuestion(submissionId: number, sequence: number) {
    // avoid header sequence 1
    sequence = Number(sequence) + 1;

    // Get submission and question block in parallel
    const [submission, submissionResponses] = await Promise.all([
      // use EntityManager instead of Repository
      this.entityManager.findOne(Submission, {
        where: { id: submissionId },
      }),
      // use Repository instead of EntityManager
      this.entityManager.findOne(Submission, {
        where: { id: submissionId },
        relations: {
          responses: {
            question: {
              itemBlock: true,
            },
          },
        },
        select: {
          responses: {
            question: {
              itemBlock: {
                id: true,
                sequence: true,
              },
              itemBlockId: true,
            },
          },
        },
      }),
    ]);

    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${submissionId} not found`,
      );
    }

    const questionBlock = await this.itemBlockRepository.findOne({
      where: {
        sequence,
        assessmentId: submission.assessmentId,
      },
      relations: {
        questions: true,
        options: true,
      },
    });

    if (!questionBlock) {
      throw new NotFoundException(
        `Question block with sequence ${sequence} not found`,
      );
    }

    const totalQuestions = await this.itemBlockRepository.count({
      where: { assessmentId: submission.assessmentId },
    });

    // Initialize question list with optimized array creation
    const questionList = new Array(totalQuestions).fill(null).map((_, i) => ({
      sequence: i + 1,
      isDone: false,
    }));

    // Mark completed questions based on responses
    if (submissionResponses?.responses) {
      const responseMap = new Set(
        submissionResponses.responses.map(
          (response) => response.question.itemBlock.sequence,
        ),
      );

      questionList.forEach((question) => {
        question.isDone = responseMap.has(question.sequence);
      });
    }

    // if user have response and response from itemblock with many question
    if (questionBlock && questionBlock.options.length > 0) {
      questionBlock.options?.forEach((option) => {
        delete option.value;
      });
    }

    // if user have response and response from itemblock with many question
    const response = submissionResponses?.responses?.filter(
      (response) => response.question.itemBlockId === questionBlock.id,
    );

    return {
      questionBlock,
      response: response || null,
      isLast: sequence === totalQuestions,
      questionList,
    };
  }

  async findItemOne(itemBlockId: number) {
    return this.itemBlockRepository.findOne({
      where: {
        id: itemBlockId,
      },
      relations: ['questions', 'options'],
    });
  }
}
