import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import type { DataParams, DataResponse } from 'src/types/params';
import { Submission } from '../entities/submission.entity';
import { Assessment } from '../entities/assessment.entity';
import { ChartData } from '../dto/chart-data.dto';


@Injectable()
export class EvaluateDashBoardService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
  ) {}
async getChartData(assessmentId: number): Promise<ChartData[]> {
    const chartDatas: ChartData[] = [];

    const assessment = await this.assessmentRepo
      .createQueryBuilder('a')
      .leftJoinAndSelect('a.itemBlocks', 'ib')
      .leftJoinAndSelect('ib.headerBody', 'h') // ความสัมพันธ์กับ HEADER_BODIES
      .leftJoinAndSelect('ib.questions', 'q')
      .leftJoinAndSelect('q.responses', 'r')
      .leftJoinAndSelect('r.selectedOption', 'so') // ตัวเลือกที่ถูกเลือกจาก responses
      .leftJoinAndSelect('ib.options', 'o') // ตัวเลือกทั้งหมดของ item block
      .where('a.id = :id', { id: assessmentId })
      .andWhere('ib.type NOT IN (:...types)', {
        types: ['UPLOAD', 'IMAGE'],
      })
      .orderBy('q.sequence', 'ASC')
      .addOrderBy('o.sequence', 'ASC')
      .getOne();

    if (!assessment) {
      throw new NotFoundException(
        `Assessment with ID ${assessmentId} not found`,
      );
    }

    // เก็บ section ที่ได้ประมวลผลสำหรับ header ไว้แล้ว
    const processedHeaderSections = new Set<number>();

    for (const itemBlock of assessment.itemBlocks) {
      // ตรวจสอบว่า type เป็น 'HEADER' และยังไม่ได้ประมวลผล header สำหรับ section นี้
      console.log(itemBlock.type);
      
      if (itemBlock.type === 'HEADER' && !processedHeaderSections.has(itemBlock.section) && itemBlock.section !== 1) {
        chartDatas.push({
          section: itemBlock.section,
          sequence: itemBlock.sequence || itemBlock.sequence || 1, // ใช้ sequence จาก headerBody ถ้ามี หรือใช้จาก itemBlock
          title: itemBlock.headerBody?.title || `หัวข้อที่ ${itemBlock.section}`, // ใช้ title จาก headerBody ถ้ามี
          labels: [],
          datasets: [{ label: '', values: [] }],
          type: 'header',
        });
        processedHeaderSections.add(itemBlock.section); // ทำเครื่องหมายว่า section นี้ถูกประมวลผลแล้ว
      }

      const type = ['RADIO', 'CHECKBOX', 'GRID'].includes(itemBlock.type)
        ? 'choice'
        : 'text';

      let title = '';
      if (type === 'text') {
        const textQuestions = itemBlock.questions.filter(
          (q) => q.isHeader === false,
        );
        title = textQuestions.length > 0 ? textQuestions[0].questionText : '';
      } else {
        const titleQuestion = itemBlock.questions.find(
          (q) => q.isHeader === true,
        );
        title = titleQuestion ? titleQuestion.questionText : '';
      }

      if (type === 'choice') {
        const questions = itemBlock.questions
          .filter((q) => q.isHeader === false)
          .sort((a, b) => a.sequence - b.sequence);

        const options = itemBlock.options
          ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
          : [];

        const labels = questions.map((q) => q.questionText);
        const datasets = options.map((option) => {
          const values = questions.map((question) => {
            const responseCount = question.responses
              ? question.responses.filter(
                  (r) => r.selectedOption && r.selectedOption.id === option.id,
                ).length
              : 0;
            return responseCount;
          });
          return { label: option.optionText, values };
        });

        chartDatas.push({
          section: itemBlock.section,
          sequence: itemBlock.sequence,
          title,
          labels,
          datasets,
          type,
        });
      } else {
        const questions = itemBlock.questions.filter(
          (q) => q.isHeader === false,
        );
        const allResponses = questions.flatMap((q) => q.responses || []);

        const options = itemBlock.options
          ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
          : [];

        const datasets = options.map((option) => {
          const count = allResponses.filter(
            (r) => r.selectedOption && r.selectedOption.id === option.id,
          ).length;
          return {
            label: option.optionText,
            values: [count],
          };
        });

        if (datasets.length > 0) {
          chartDatas.push({
            section: itemBlock.section,
            sequence: itemBlock.sequence,
            title,
            labels: [], // ไม่มีหลายคำถาม จึงไม่มี label
            datasets,
            type,
          });
        }
      }
    }

    return chartDatas.sort((a, b) => {
      if (a.section !== b.section) {
        return a.section - b.section;
      }
      return a.sequence - b.sequence;
    });
}

  async getNumberOfResponses(assessmentId: number): Promise<number> {
    const result = await this.assessmentRepo.query(
      `SELECT A.name, COUNT(*) as number
     FROM assessments AS A
     JOIN submissions AS S ON A.id = S.assessmentId
     WHERE A.id = ${assessmentId}`,
    );
    return result[0]; // แปลงผลลัพธ์เป็น number
  }

}


