import {
  Injectable,
  NotFoundException,
  BadRequestException,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Raw } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CreateAssessmentDto } from '../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../dto/updates/update-assessment.dto';
import { Assessment } from '../entities/assessment.entity';
import { Program } from '../../programs/entities/program.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { AssessmentConfig } from 'src/configs/assessment.config';
import type { DataParams, DataResponse } from 'src/types/params';
import { AssessmentType } from '../enums/assessment-type.enum';
import { HeaderBodiesService } from '../header-bodies/header-bodies.service';
import { ItemBlocksService } from '../item-blocks/item-blocks.service';
import { StartQuizDto } from '../dto/start-quiz.dto';
import { Submission } from '../entities/submission.entity';

@Injectable()
export class QuizService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    private itemBlocksService: ItemBlocksService,
    private headerBodiesService: HeaderBodiesService,
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
  ) {}

  async getAll(
    query: DataParams,
    type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    const { page, limit, sortBy, order, search } = query;

    const whereConditions: any = {
      type: type,
    };

    if (search) {
      whereConditions.name = Raw(
        (alias) => `LOWER(${alias}) LIKE LOWER(:search)`,
        { search: `%${search}%` },
      );
    }

    const orderOptions: any = {};
    if (sortBy) {
      orderOptions[sortBy] = (order || 'ASC').toUpperCase();
    } else {
      orderOptions.id = 'ASC';
    }

    const [data, total] = await this.assessmentRepository.findAndCount({
      where: whereConditions,
      relations: ['creator'],
      order: orderOptions,
      skip: (page - 1) * limit,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  async findOne(id: number): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
      // relations: ['creator', 'program', 'itemBlocks', 'submissions'],
      relations: [
        'creator',
        'program',
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID "${id}" not found`);
    }
    return assessment;
  }

  // FIXME: using update block instead of update assessment
  async update(
    id: number,
    updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    if (!updateAssessmentDto || Object.keys(updateAssessmentDto).length === 0) {
      throw new BadRequestException('No update values provided');
    }
    const assessment = await this.findOne(id);
    const updatedAssessment = this.assessmentRepository.merge(
      assessment,
      updateAssessmentDto,
    );
    return this.assessmentRepository.save(updatedAssessment);
  }

  async remove(id: number) {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    await this.assessmentRepository.remove(assessment);
    return { success: true };
  }

  async getAvailableQuiz(
    userId: number,
    query: DataParams,
  ): Promise<DataResponse<Assessment>> {
    const { page, limit, sortBy, order } = query;

    const qb = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.program', 'program')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .leftJoin(
        'assessment.submissions',
        'submissions',
        'submissions.userId = :userId',
        { userId },
      )
      .where('assessment.type = :type', { type: AssessmentType.QUIZ })
      .andWhere('assessment.isPrototype = :isPrototype', {
        isPrototype: false,
      });

    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'DESC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.createdAt', 'DESC');
    }

    qb.skip((page - 1) * limit).take(limit);

    const [data, total] = await qb.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }
}
