import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import type { DataParams, DataResponse } from 'src/types/params';
import { Submission } from '../entities/submission.entity';
import { Question } from '../entities/question.entity';
import { Assessment } from '../entities/assessment.entity';
import { AssessmentValidator } from '../helper/assessments.validator';

export interface AssessmentMeta {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: string;
  options: OptionResponseData[];
  chartData: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
    }[];
  };
}

export interface OptionResponseData {
  optionId: number;
  orderInQuestion: number;
  optionText: string;
  selectionCount: number;
  isCorrectAnswer: boolean;
}

export interface ParticipantData {
  id: number;
  date: string;
  userName: string;
  score: number;
}

@Injectable()
export class QuizDashboardService {
  constructor(
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    private assessmentValidator: AssessmentValidator,
  ) {}

  async generateAssessmentMeta(
    assessmentId: number,
    assessmentName?: string,
  ): Promise<AssessmentMeta> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const [uniqueUsers, scoreStats] = await Promise.all([
      this.getUniqueUsersCount(assessmentId),
      this.getScoreStatistics(assessmentId),
    ]);

    return {
      assessmentName: assessmentName || `Assessment #${assessmentId}`,
      uniqueUsers,
      ...scoreStats,
    };
  }

  async generateScoreDistributionChart(
    assessmentId: number,
  ): Promise<ChartData> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const submissionCount = await this.submissionRepository.count({
      where: { assessmentId },
    });

    if (submissionCount === 0) {
      return this.createEmptyChart();
    }

    const scoreDistribution = await this.getScoreDistribution(assessmentId);
    return this.formatScoreChart(scoreDistribution);
  }

  async generateAllQuestionResponses(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<QuestionResponseData>> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const questionsData = await this.getQuestionsForAssessment(
      assessmentId,
      query,
    );

    if (questionsData.total === 0) {
      return {
        data: [],
        total: 0,
        curPage: questionsData.curPage,
        hasPrev: false,
        hasNext: false,
      };
    }

    const responses = await Promise.all(
      questionsData.data.map((question) =>
        this.buildQuestionResponse(question),
      ),
    );

    return {
      data: responses,
      total: questionsData.total,
      curPage: questionsData.curPage,
      hasPrev: questionsData.hasPrev,
      hasNext: questionsData.hasNext,
    };
  }
  async getAllParticipants(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<ParticipantData>> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const { page = 1, limit = 10, sortBy, order, search } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereCondition: any = { assessmentId };
    const relations = ['user', 'responses', 'responses.selectedOption'];

    if (search) {
      whereCondition.user = { name: Like(`%${search}%`) };
    }

    const orderCondition: any = {};
    if (sortBy === 'userName') {
      orderCondition.user = { name: (order || 'ASC').toUpperCase() };
    } else if (sortBy === 'date') {
      orderCondition.startAt = (order || 'ASC').toUpperCase();
    } else if (sortBy) {
      orderCondition[sortBy] = (order || 'ASC').toUpperCase();
    } else {
      orderCondition.id = 'ASC';
    }

    const [submissions, total] = await this.submissionRepository.findAndCount({
      where: whereCondition,
      relations,
      order: orderCondition,
      take: Number(limit),
      skip,
    });

    const formattedParticipants = submissions.map((submission) => {
      const score =
        submission.responses?.reduce((total, response) => {
          return total + (response.selectedOption?.value || 0);
        }, 0) || 0;

      return {
        id: submission.id,
        date: submission.startAt.toISOString().split('T')[0],
        userName: submission.user?.name || '',
        score,
      };
    });

    return {
      data: formattedParticipants,
      total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  async getOneParticipant(submissionId: number) {
    const submission = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.user', 'user')
      .leftJoinAndSelect('submission.assessment', 'assessment')
      .where('submission.id = :submissionId', { submissionId })
      .getOne();

    if (!submission) {
      return null;
    }

    const responses = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.responses', 'response')
      .leftJoinAndSelect('response.question', 'question')
      .leftJoinAndSelect('question.itemBlock', 'itemBlock')
      .leftJoinAndSelect('response.selectedOption', 'option')
      .leftJoinAndSelect('itemBlock.options', 'allOptions')
      .where('submission.id = :submissionId', { submissionId })
      .orderBy('itemBlock.sequence', 'ASC')
      .addOrderBy('question.sequence', 'ASC')
      .getOne();

    let totalScore = 0;
    let totalPossibleScore = 0;

    if (responses && responses.responses) {
      responses.responses.forEach((response) => {
        if (response.selectedOption && response.selectedOption.value) {
          totalScore += response.selectedOption.value;
        }

        if (
          response.question &&
          response.question.itemBlock &&
          response.question.itemBlock.options
        ) {
          const maxValue = Math.max(
            ...response.question.itemBlock.options.map((opt) => opt.value || 0),
          );
          totalPossibleScore += maxValue;
        }
      });
    }

    const formattedQuestions =
      responses?.responses?.map((response) => {
        const question = response.question;
        const selectedOption = response.selectedOption;
        const allOptions = question.itemBlock.options;

        return {
          questionId: question.id,
          questionText: question.questionText,
          questionSequence: question.sequence,
          section: question.itemBlock.sequence,
          selectedOptionId: selectedOption?.id,
          selectedOptionText: selectedOption?.optionText,
          isCorrect: selectedOption?.value > 0,
          score: selectedOption?.value || 0,
          options: allOptions.map((option) => ({
            id: option.id,
            text: option.optionText,
            sequence: option.sequence,
            value: option.value,
            isSelected: option.id === selectedOption?.id,
          })),
        };
      }) || [];

    return {
      submissionId: submission.id,
      assessmentId: submission.assessmentId,
      assessmentName: submission.assessment.name,
      userId: submission.user.id,
      userName: submission.user.name,
      startTime: submission.startAt,
      endTime: submission.endAt,
      totalScore: totalScore,
      maxScore: totalPossibleScore,
      scorePercentage:
        totalPossibleScore > 0 ? (totalScore / totalPossibleScore) * 100 : 0,
      questions: formattedQuestions,
    };
  }

  private async getUniqueUsersCount(assessmentId: number): Promise<number> {
    const result = await this.submissionRepository
      .createQueryBuilder('submission')
      .select('COUNT(DISTINCT submission.userId)', 'uniqueUsers')
      .where('submission.assessmentId = :assessmentId', { assessmentId })
      .getRawOne();

    return parseInt(result?.uniqueUsers || '0');
  }

  private async getScoreStatistics(assessmentId: number) {
    const result = await this.submissionRepository
      .createQueryBuilder()
      .select([
        'MAX(total_score) as highestScore',
        'MIN(total_score) as lowestScore',
      ])
      .from((subQuery) => {
        return subQuery
          .select([
            'submission.id',
            'COALESCE(SUM(option.value), 0) as total_score',
          ])
          .from(Submission, 'submission')
          .leftJoin('submission.responses', 'response')
          .leftJoin('response.selectedOption', 'option')
          .where('submission.assessmentId = :assessmentId', { assessmentId })
          .groupBy('submission.id');
      }, 'submission_scores')
      .getRawOne();

    return {
      highestScore: parseFloat(result?.highestScore || '0'),
      lowestScore: parseFloat(result?.lowestScore || '0'),
    };
  }

  private createEmptyChart(): ChartData {
    return {
      labels: ['No Data'],
      datasets: [{ data: [1], backgroundColor: ['#e0e0e0'] }],
    };
  }

  private async getScoreDistribution(assessmentId: number) {
    return this.submissionRepository
      .createQueryBuilder()
      .select([
        `CASE 
          WHEN score_percentage <= 20 THEN '0-20'
          WHEN score_percentage <= 40 THEN '21-40'
          WHEN score_percentage <= 60 THEN '41-60'
          WHEN score_percentage <= 80 THEN '61-80'
          ELSE '81-100'
        END as scoreRange`,
        'COUNT(*) as count',
      ])
      .from((subQuery) => {
        return subQuery
          .select([
            'submission.id',
            `CASE 
              WHEN SUM(CASE WHEN option.value > 0 THEN option.value ELSE 0 END) > 0 
              THEN (COALESCE(SUM(CASE WHEN response.selectedOptionId IS NOT NULL THEN option.value ELSE 0 END), 0) * 100.0 / SUM(CASE WHEN option.value > 0 THEN option.value ELSE 0 END))
              ELSE 0
            END as score_percentage`,
          ])
          .from(Submission, 'submission')
          .leftJoin('submission.responses', 'response')
          .leftJoin('response.selectedOption', 'option')
          .leftJoin('response.question', 'question')
          .leftJoin('question.itemBlock', 'itemBlock')
          .leftJoin('itemBlock.options', 'allOptions')
          .where('submission.assessmentId = :assessmentId', { assessmentId })
          .groupBy('submission.id');
      }, 'scores')
      .groupBy(
        `CASE 
        WHEN score_percentage <= 20 THEN '0-20'
        WHEN score_percentage <= 40 THEN '21-40'
        WHEN score_percentage <= 60 THEN '41-60'
        WHEN score_percentage <= 80 THEN '61-80'
        ELSE '81-100'
      END`,
      )
      .getRawMany();
  }

  private formatScoreChart(scoreDistribution: any[]): ChartData {
    const scoreRanges = {
      '0-20': { count: 0, color: '#FF6384' },
      '21-40': { count: 0, color: '#FFCE56' },
      '41-60': { count: 0, color: '#36A2EB' },
      '61-80': { count: 0, color: '#4BC0C0' },
      '81-100': { count: 0, color: '#9966FF' },
    };

    scoreDistribution.forEach((result) => {
      const range = result.scoreRange;
      if (scoreRanges[range]) {
        scoreRanges[range].count = parseInt(result.count);
      }
    });

    return {
      labels: Object.keys(scoreRanges),
      datasets: [
        {
          data: Object.values(scoreRanges).map((range) => range.count),
          backgroundColor: Object.values(scoreRanges).map(
            (range) => range.color,
          ),
        },
      ],
    };
  }

  private async getQuestionsForAssessment(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<any>> {
    const { page = 1, limit = 10, sortBy, order, search } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereCondition: any = {
      itemBlock: { assessment: { id: assessmentId } },
    };

    if (search) {
      whereCondition.questionText = Like(`%${search}%`);
    }

    const relations = ['itemBlock', 'itemBlock.assessment'];

    const orderCondition: any = {};
    if (sortBy === 'orderInQuiz') {
      orderCondition.itemBlock = { sequence: (order || 'ASC').toUpperCase() };
    } else if (sortBy) {
      orderCondition[sortBy] = (order || 'ASC').toUpperCase();
    } else {
      orderCondition.itemBlock = { sequence: 'ASC' };
    }

    const [questions, total] = await this.questionRepository.findAndCount({
      where: whereCondition,
      relations,
      order: orderCondition,
      take: Number(limit),
      skip,
    });

    const formattedQuestions = questions.map((question) => ({
      question_id: question.id,
      order_in_quiz: question.itemBlock.sequence,
      question_text: question.questionText,
      question_type: question.itemBlock.type,
    }));

    return {
      data: formattedQuestions,
      total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  private async buildQuestionResponse(
    question: any,
  ): Promise<QuestionResponseData> {
    const options = await this.getQuestionOptions(question.question_id);
    const chartData = this.createQuestionChart(options);

    return {
      questionId: question.question_id,
      orderInQuiz: question.order_in_quiz,
      questionText: question.question_text,
      questionType: question.question_type,
      options,
      chartData,
    };
  }

  private async getQuestionOptions(
    questionId: number,
  ): Promise<OptionResponseData[]> {
    const optionsData = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.responses', 'response')
      .leftJoin('response.selectedOption', 'option')
      .leftJoin('response.question', 'question')
      .select([
        'option.id as option_id',
        'option.sequence as order_in_question',
        'option.optionText as option_text',
        'COUNT(response.selectedOptionId) as selection_count',
        'CASE WHEN option.value > 0 THEN true ELSE false END as is_correct_answer',
      ])
      .where('question.id = :questionId', { questionId })
      .andWhere('option.id IS NOT NULL')
      .groupBy('option.id')
      .orderBy('option.sequence', 'ASC')
      .getRawMany();

    return optionsData.map((result) => ({
      optionId: parseInt(result.option_id),
      orderInQuestion: parseInt(result.order_in_question),
      optionText: result.option_text,
      selectionCount: parseInt(result.selection_count),
      isCorrectAnswer:
        result.is_correct_answer === 'true' ||
        result.is_correct_answer === true,
    }));
  }

  private createQuestionChart(options: OptionResponseData[]): ChartData {
    const colors = [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF',
      '#FF9F40',
    ];

    return {
      labels: options.map((option) => option.optionText),
      datasets: [
        {
          data: options.map((option) => option.selectionCount),
          backgroundColor: options.map(
            (_, index) => colors[index % colors.length],
          ),
        },
      ],
    };
  }
}
