import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { Response } from './response.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('submissions')
export class Submission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'datetime', comment: 'เริ่มทำ' })
  startAt: Date;

  @Column({ type: 'datetime', comment: 'หมดเวลา' })
  endAt: Date;

  @Column({ type: 'datetime', nullable: true, comment: 'วันที่ส่ง' })
  submitAt: Date | null;

  @Column()
  userId: number;

  @Column()
  assessmentId: number;

  @ManyToOne(() => User, (user) => user.submissions)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Assessment, (assessment) => assessment.submissions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'assessmentId' })
  assessment: Assessment;

  @OneToMany(() => Response, (response) => response.submission, {
    cascade: true,
  })
  responses: Response[];
}
