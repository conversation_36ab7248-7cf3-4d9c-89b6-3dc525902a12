import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  ParseIntPipe,
  ParseBoolPipe,
} from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';

@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        perNameTh: { type: 'string', example: 'สิทธิ์การเข้าดูผู้ใช้' },
        perNameEn: { type: 'string', example: 'View Users' },
        perStatus: { type: 'boolean', default: true, example: true },
        perDefault: { type: 'boolean', example: false },
      },
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createPermissionDto: CreatePermissionDto) {
    return this.permissionsService.create(createPermissionDto);
  }

  @Get()
  findAll() {
    return this.permissionsService.findAll();
  }

  @Get('/status')
  findAllByStatus(@Query('perStatus', ParseBoolPipe) perStatus: boolean) {
    return this.permissionsService.findAllByStatus(perStatus);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.permissionsService.findOne(id);
  }

  @Patch(':id/set-status')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        perStatus: { type: 'boolean', example: true },
      },
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  setStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ) {
    return this.permissionsService.setPermissionStatus(id, updatePermissionDto.perStatus);
  }


  @Patch(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        perNameTh: { type: 'string', example: 'สิทธิ์การเข้าดูผู้ใช้' },
        perNameEn: { type: 'string', example: 'View Users' },
        perStatus: { type: 'boolean', example: true },
        perDefault: { type: 'boolean', example: false },
      },
      required: ['perNameTh', 'perNameEn'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ) {
    return this.permissionsService.update(id, updatePermissionDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.permissionsService.remove(id);
  }
}
